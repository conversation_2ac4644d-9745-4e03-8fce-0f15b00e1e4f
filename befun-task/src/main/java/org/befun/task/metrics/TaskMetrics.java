package org.befun.task.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.task.configuration.TaskMetricsProperties;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TaskMetrics {

    @Autowired(required = false)
    private MeterRegistry meterRegistry;
    @Autowired
    private TaskMetricsProperties properties;

    private final Map<String/*{queue}/{type}*/, Counter> counterMap = new HashMap<>();

    private static final String METRIC_NAME_PREFIX = "befun.task";
    private static final String METRIC_NAME_APP_COUNT = METRIC_NAME_PREFIX + ".app.count";  // tags: queue, type(producer), namespace, deployment, pod

    private static final String TYPE_PRODUCER = "producer";
    private static final String TYPE_CONSUMER = "consumer";

    public void producerIncrement(String queue) {
        increment(TYPE_PRODUCER, queue);
    }

    public void consumerIncrement(String queue) {
        increment(TYPE_CONSUMER, queue);
    }

    private void increment(String type, String queue) {
        if (meterRegistry != null && !properties.getSupportQueues().contains(queue)) {
            return;
        }
        String key = queue + "/" + type;
        Counter counter = counterMap.get(key);
        if (counter == null) {
            synchronized (counterMap) {
                counter = counterMap.get(key);
                if (counter == null) {
                    counter = createAppCounter(queue, type);
                    counterMap.put(key, counter);
                }
            }
        }
        counter.increment();
    }

    private Counter createAppCounter(String queue, String type) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("type", type));
        tags.add(Tag.of("queue", queue));
        return meterRegistry.counter(METRIC_NAME_APP_COUNT, tags);
    }

}
