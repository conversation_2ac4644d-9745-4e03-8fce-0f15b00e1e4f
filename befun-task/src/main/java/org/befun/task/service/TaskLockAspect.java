package org.befun.task.service;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.befun.task.annotation.TaskLock;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;

@Slf4j
@Aspect
public class TaskLockAspect {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Pointcut("@annotation(org.befun.task.annotation.TaskLock)")
    public void lockTask() {
    }

    @Around("lockTask()")
    public Object applyLock(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sig = (MethodSignature) joinPoint.getSignature();
        Method method = sig.getMethod();
        TaskLock lock = method.getAnnotation(TaskLock.class);
        String key = taskService.getWorkerProperty().getPrefix() + ".lock." + lock.key();
        Boolean lockResult = stringRedisTemplate.opsForValue().setIfAbsent(key, "true", Duration.ofSeconds(lock.seconds()));
        if (lockResult != null && lockResult) {
            try {
                return joinPoint.proceed();
            } catch (Exception ex) {
                log.error("error to deal lock task {}", ex.getMessage());
                ex.printStackTrace();
            } finally {
                stringRedisTemplate.delete(key);
            }
        } else {
            log.info("look like the method is busy, {} will skip", method.getName());
        }
        return null;
    }
}
