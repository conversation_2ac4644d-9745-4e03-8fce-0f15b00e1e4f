package org.befun.core.entity;

/**
 * The class description
 *
 * <AUTHOR>
 */

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.UpgradeTaskStatus;
import org.befun.core.dto.annotation.DtoProperty;

@Entity
@Getter
@Setter
@Table(name = "upgrade_task")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeTask extends BaseEntity {

    @Column(name = "task_name")
    @DtoProperty
    private String taskName;

    @Column(name = "status")
    @DtoProperty
    @Enumerated
    private UpgradeTaskStatus status;

    @Column(name = "upgrade_time")
    @DtoProperty
    private Date upgradeTime;
}