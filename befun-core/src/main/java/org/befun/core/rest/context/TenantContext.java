package org.befun.core.rest.context;

import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.dto.UserPermissions;
import org.springframework.security.authentication.AccountExpiredException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TenantContext {
    private static final ThreadLocal<Long> currentTenant = new ThreadLocal<>();
    private static final ThreadLocal<Long> currentUserId = new ThreadLocal<>();
    private static final ThreadLocal<List<Long>> currentDepartmentIds = new ThreadLocal<>();
    private static final ThreadLocal<List<Long>> currentSubDepartmentIds = new ThreadLocal<>();
    private static final ThreadLocal<List<Long>> currentRoleIds = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> currentIsAdmin = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> currentIsTop = new ThreadLocal<>();
    private static final ThreadLocal<UserPermissions> currentPermissions = new ThreadLocal<>();
    private static final ThreadLocal<Map<Class<?>, EntityScopeStrategyTypes>> customEntityScopeStrategy = new ThreadLocal<>();
    private static final ThreadLocal<List<String>> currentUserEvents = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, Object>> extFilterParams = new ThreadLocal<>();

    private static final ThreadLocal<String> currentSessionKey = new ThreadLocal<>();

    /**
     * 有的接口会清空线程变量里的用户数据，这里备份一次
     */
    public static final ThreadLocal<TenantData> copiedTenantData = new ThreadLocal<>();

    // 数据书否需要脱敏 默认false
    private static final ThreadLocal<Boolean> needMask = ThreadLocal.withInitial(() -> false);

    public static Long getCurrentTenant() {
        return currentTenant.get();
    }

    public static long requireCurrentTenant() {
        return Optional.ofNullable(getCurrentTenant()).orElseThrow(() -> new AccountExpiredException("请重新登录"));
    }

    public static void setCurrentTenant(Long tenant) {
        currentTenant.set(tenant);
    }

    public static Long getCurrentUserId() {
        return currentUserId.get();
    }

    public static long requireCurrentUserId() {
        return Optional.ofNullable(getCurrentUserId()).orElseThrow(() -> new AccountExpiredException("请重新登录"));
    }

    public static void setCurrentUserId(Long userId) {
        currentUserId.set(userId);
    }

    public static String getCurrentSessionKey() {
        return currentSessionKey.get();
    }

    public static String requireCurrentSessionKey() {
        return Optional.ofNullable(getCurrentSessionKey()).orElseThrow(() -> new AccountExpiredException("请重新登录"));
    }

    public static void setCurrentSessionKey(String userId) {
        currentSessionKey.set(userId);
    }


    public static List<Long> getCurrentRoleIds() {
        return currentRoleIds.get();
    }

    public static void setCurrentRoleIds(List<Long> roleIds) {
        currentRoleIds.set(roleIds);
    }

    public static List<Long> getCurrentDepartmentIds() {
        return currentDepartmentIds.get();
    }

    public static void setCurrentDepartmentIds(List<Long> departmentIds) {
        currentDepartmentIds.set(departmentIds);
    }

    public static List<Long> getCurrentSubDepartmentIds() {
        return currentSubDepartmentIds.get();
    }

    public static void setCurrentSubDepartmentIds(List<Long> subDepartmentId) {
        currentSubDepartmentIds.set(subDepartmentId);
    }

    public static Boolean getCurrentIsAdmin() {
        return currentIsAdmin.get();
    }

    public static void setCurrentIsAdmin(Boolean isAdmin) {
        currentIsAdmin.set(isAdmin);
    }

    public static Boolean getCurrentIsTop() {
        return currentIsTop.get();
    }

    public static void setCurrentIsTop(Boolean isTop) {
        currentIsTop.set(isTop);
    }

    public static UserPermissions getCurrentPermissions() {
        return currentPermissions.get();
    }

    public static void setCurrentPermissions(UserPermissions permission) {
        currentPermissions.set(permission);
    }

    public static void addCustomEntityScopeStrategy(Class<?> entityClass, EntityScopeStrategyTypes strategy) {
        Map<Class<?>, EntityScopeStrategyTypes> map = customEntityScopeStrategy.get();
        if (map == null) {
            map = new HashMap<>();
            customEntityScopeStrategy.set(map);
        }
        map.put(entityClass, strategy);
    }

    public static EntityScopeStrategyTypes getCustomEntityScopeStrategy(Class<?> entityClass) {
        return Optional.ofNullable(customEntityScopeStrategy.get()).map(i -> i.get(entityClass)).orElse(null);
    }

    public static void clearCustomEntityScopeStrategy(Class<?> entityClass) {
        Optional.ofNullable(customEntityScopeStrategy.get()).ifPresent(i -> i.remove(entityClass));
    }

    public static List<String> getCurrentUserEvents() {
        return currentUserEvents.get();
    }

    public static void setCurrentUserEvents(List<String> userEvents) {
        currentUserEvents.set(userEvents);
    }

    public static void addExtFilterParams(String key, Object value) {
        Map<String, Object> map = extFilterParams.get();
        if (map == null) {
            map = new HashMap<>();
            extFilterParams.set(map);
        }
        map.put(key, value);
    }

    public static void clearExtFilterParams(String key) {
        Optional.ofNullable(extFilterParams.get()).ifPresent(i -> i.remove(key));
    }

    public static Object getExtFilterParams(String key) {
        return Optional.ofNullable(extFilterParams.get()).map(i -> i.get(key)).orElse(null);
    }

    public static Boolean needMask() {
        return needMask.get();
    }

    public static void setNeedMask(Boolean needMask) {
        TenantContext.needMask.set(needMask);
    }


    public static void clear() {
        copiedTenantData.set(new TenantData(
                currentTenant.get(),
                currentUserId.get(),
                currentDepartmentIds.get(),
                currentSubDepartmentIds.get(),
                currentRoleIds.get()));
        currentTenant.set(null);
        currentUserId.set(null);
        currentDepartmentIds.set(null);
        currentSubDepartmentIds.set(null);
        currentRoleIds.set(null);
        currentIsAdmin.set(null);
        currentIsTop.set(null);
        currentPermissions.set(null);
        customEntityScopeStrategy.set(null);
        currentUserEvents.set(null);
        extFilterParams.set(null);
        currentSessionKey.set(null);
        needMask.set(false);
    }
}