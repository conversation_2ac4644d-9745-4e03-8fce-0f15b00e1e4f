package org.befun.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.befun.core.configuration.ApplicationProperties;
import org.befun.core.rest.ResourceAccessDeniedHandler;
import org.befun.core.rest.ResponseExceptionHandler;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.security.OpenApiTokenFilter;
import org.befun.core.utils.JsonHelper;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebMvc
@EnableWebSecurity
//@EntityScan("org.befun.core.entity")
@ComponentScan({"org.befun.core.service"})
@EnableGlobalMethodSecurity(prePostEnabled = true)
@EnableConfigurationProperties(ApplicationProperties.class)
@Order(90)
public class AutoConfiguration extends WebSecurityConfigurerAdapter implements WebMvcConfigurer {

    @Autowired
    private ApplicationProperties applicationProperties;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedMethods("HEAD", "GET", "PUT", "POST", "DELETE", "PATCH");
    }

    @Bean("staticJsonHelper")
    public JsonHelper jsonHelper(ObjectMapper objectMapper){
        return new JsonHelper(objectMapper);
    }

    @Bean
    public ResponseExceptionHandler exceptionAdvice() {
        return new ResponseExceptionHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public LegacyAuthTokenFilter legacyTokenFilter() {
        return new LegacyAuthTokenFilter();
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "befun.server", name = "enable-open-api-filter", havingValue = "true")
    public OpenApiTokenFilter openApiTokenFilter() {
        return new OpenApiTokenFilter();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .cors()
                .and().csrf().disable()
                .anonymous()
                .and().exceptionHandling().accessDeniedHandler(new ResourceAccessDeniedHandler());

        http.addFilterBefore(legacyTokenFilter(), BasicAuthenticationFilter.class);
        if (applicationProperties.getEnableOpenApiFilter()) {
            http.addFilterAfter(openApiTokenFilter(), LegacyAuthTokenFilter.class);
        }
        /**
         * Security默认的登出地址为：/logout，在登出后会进行如下操作：
         * 移除HttpSession
         * 清空配置的RememberMe认证
         * 清空SecurityContextHolder`
         * 重定向到/login?logout
         **/
        http.logout().disable();
    }

    @Bean
    public ModelMapper modelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setSkipNullEnabled(true)
                .setCollectionsMergeEnabled(false)
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setFieldMatchingEnabled(true)
                .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PACKAGE_PRIVATE);
        return modelMapper;
    }
}
