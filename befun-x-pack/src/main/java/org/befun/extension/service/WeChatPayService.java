package org.befun.extension.service;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.befun.extension.Extensions;
import org.befun.extension.property.WeChatPayProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Slf4j
@Service("xPackWeChatPayService")
@ConditionalOnProperty(name = Extensions.WX_PAY_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.WX_PAY_MATCH_IF_MISSING)
public class WeChatPayService extends WxPayServiceImpl {

    public WeChatPayService(WeChatPayProperty property) {
        WxPayConfig config = new WxPayConfig();
        config.setAppId(property.getAppId());
        config.setMchId(property.getMchId());
        config.setMchKey(property.getV2MchKey());
        config.setApiV3Key(property.getV3MchKey());
        config.setUseSandboxEnv(property.isUseSandbox());
        config.setNotifyUrl(property.getPayNotifyUrl());
        config.setKeyPath(property.getCertP12Path());
        config.setPrivateKeyPath(property.getPrivateKeyPath());
        config.setPrivateCertPath(property.getCertPemPath());
        addConfig(property.getMchId(), config);
    }

}
