package org.befun.extension.service;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.generator.SysConvert;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.CreateLinkDto;
import org.befun.extension.dto.CreateLinkResponseDto;
import org.befun.extension.dto.XpackClientConfigDto;
import org.befun.extension.entity.Link;
import org.befun.extension.entity.LinkExist;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.property.LinkProperty;
import org.befun.extension.repository.LinkExistRepository;
import org.befun.extension.repository.LinkRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class LinkService {

    @Autowired
    private LinkProperty   linkProperty;

    @Autowired
    private LinkRepository linkRepository;

    @Autowired
    private LinkExistRepository linkExistRepository;
    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    private final String linkClientConfigKey = "xpack:link:client:config";


    private XpackClientConfigDto getClientConfig() {
        Long orgId = TenantContext.requireCurrentTenant();
        String key = linkClientConfigKey+":"+ orgId;
        String cacheValue = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return JsonHelper.toObject(cacheValue, XpackClientConfigDto.class);
        }

        AtomicReference<XpackClientConfigDto> xpackClientConfigDto = new AtomicReference<>(new XpackClientConfigDto(
            linkProperty.getSurveyClientPrefix(), linkProperty.getRoot()
        ));

        XpackConfig xpackConfig = xpackConfigService.getConfigByTypeAndSubType(XPackAppType.CLIENT_CONFIG, Objects.toString(orgId));
        Optional.ofNullable(xpackConfig).ifPresent(
                config->{
                    XpackClientConfigDto dto = JsonHelper.toObject(config.getConfig(), XpackClientConfigDto.class);
                    stringRedisTemplate.opsForValue().set(key,config.getConfig(), Duration.ofHours(1));
                    xpackClientConfigDto.set(dto);
                });
        return xpackClientConfigDto.get();

    }

    public Link getLink(Long id) {
        return linkRepository.findById(id).orElse(null);
    }

    public Link getLink(String code) {
        long id = SysConvert.toDecimal(code);
        if (id <= 0) {
            return null;
        }
        return linkRepository.findById(id).orElse(null);
    }

    public String findUrlById(Long id) {
        Link link = getLink(id);
        if (link != null) {
            return link.getUrl();
        } else {
            return null;
        }
    }

    /**
     * 短链编码获取问卷id
     */
    public Long getSurveyIdByCode(String code) {
        Link link = getLink(code);
        if (link != null) {
            checkIfNeedParseLink(link);
            return link.getSurveyId();
        } else {
            return null;
        }
    }

    /**
     * 短链编码获取问卷链接参数
     */
    public Map<String, Object> getSurveyParamsByCode(String code) {
        Link link = getLink(code);
        Long surveyId = null;
        Map<String, Object> params = null;
        if (link != null) {
            checkIfNeedParseLink(link);
            surveyId = link.getSurveyId();
            params = JsonHelper.toMap(link.getParams());
        }
        if (params == null) {
            params = new HashMap<>();
        }
        if (surveyId != null && !params.containsKey("surveyId")) {
            params.put("surveyId", surveyId);
        }
        return params;
    }

    /**
     * 短链编码获取重定向的地址
     * 这里兼容了旧的短链接，如果是旧的，则会解析原始的url
     */
    @Transactional
    public String parseRedirectUrl(String code) {
        Link link = getLink(code);
        if (link == null) {
            return null;
        }
        checkIfNeedParseLink(link);
        if (link.getSurveyId() != null) {
            return requireSurveyClientPrefix() + "/" + SysConvert.toX(link.getId());
        } else {
            return link.getUrl();
        }
    }

    private void checkIfNeedParseLink(Link link) {
        if (link.getSurveyId() == null && link.getUrl() != null) {
            //  reset old link
            if (parseUrlToLink(link.getUrl(), link)) {
                linkRepository.save(link);
            }
        }
    }

    /**
     * 创建短链
     */
    public String toShortUrl(Long surveyId, Map<String, Object> params) {
        String shortUrlRoot = requireShortUrlRoot();
        Link link = createLink(surveyId, params);
        String code = SysConvert.toX(link.getId());
        String shortUrl = shortUrlRoot + "/" + code;
        log.debug("短链创建成功：surveyId={}, linkId={}, params={}, shortUrl={}", surveyId, link.getId(), link.getParams(), shortUrl);
        return shortUrl;
    }

    /**
     * 创建短链
     */
    public String toShortUrl(Long surveyId, Map<String, Object> params, Boolean checkExist) {
        String shortUrlRoot = requireShortUrlRoot();
        Link link = createLink(surveyId, params, checkExist);
        String code = SysConvert.toX(link.getId());
        String shortUrl = shortUrlRoot + "/" + code;
        log.debug("短链创建成功：surveyId={}, linkId={}, params={}, shortUrl={}", surveyId, link.getId(), link.getParams(), shortUrl);
        return shortUrl;
    }

    /**
     * 拼接短链
     */
    public String toShortUrl(Link link) {
        String shortUrlRoot = requireShortUrlRoot();
        String code = toShortCode(link);
        return shortUrlRoot + "/" + code;
    }

    /**
     * 转换短链编号
     */
    public String toShortCode(Link link) {
        return SysConvert.toX(link.getId());
    }

    /**
     * 拼接原始地址
     */
    public String toOriginUrl(Link link) {
        String surveyClientPrefix = requireSurveyClientPrefix();
        String code = toShortCode(link);
        return surveyClientPrefix + "/" + code;
    }


    /**
     * 创建短链
     */
    public Link createLink(Long surveyId, Map<String, Object> params) {
        return createLink(surveyId, 1, params);
    }

    /**
     * 创建短链
     */
    public Link createLink(Long surveyId, Map<String, Object> params, Boolean checkExist) {
        return createLink(surveyId, 1, params, true);
    }

    /**
     * 创建短链
     */
    public Link createLink(Long surveyId, int source, Map<String, Object> params, Boolean checkExist) {
        String surveyClientPrefix = requireSurveyClientPrefix();
        String formatParams;
        if (params == null) {
            params = new HashMap<>();
        }
        params.put("surveyId", surveyId);
        formatParams = JsonHelper.toJson(params);

        AtomicReference<Link> link = new AtomicReference<>(new Link());
        link.get().setSurveyId(surveyId);
        link.get().setParams(formatParams);
        link.get().setSource(source);
        link.get().setSurveyClientPrefix(surveyClientPrefix);


        if (checkExist) {
            Optional.ofNullable(link.get().getHash()).ifPresent(
                    hash->linkExistRepository.findFirstByHash(hash).ifPresentOrElse(
                        le -> link.set(getLink(le.getLinkId())),
                        () -> {
                            linkRepository.save(link.get());
                            LinkExist linkExist = new LinkExist(link.get());
                            linkExistRepository.save(linkExist);
                        })
            );
        }

        linkRepository.save(link.get());
        return link.get();
    }


    /**
     * 创建短链
     */
    public Link createLink(Long surveyId, int source, Map<String, Object> params) {
        String surveyClientPrefix = requireSurveyClientPrefix();
        String formatParams;
        if (params == null) {
            params = new HashMap<>();
        }
        params.put("surveyId", surveyId);
        formatParams = JsonHelper.toJson(params);

        AtomicReference<Link> link = new AtomicReference<>(new Link());
        link.get().setSurveyId(surveyId);
        link.get().setParams(formatParams);
        link.get().setSource(source);
        link.get().setSurveyClientPrefix(surveyClientPrefix);

        linkRepository.save(link.get());
        return link.get();
    }


    /**
     * 转换为短链
     *
     * @param url         原始地址
     * @param forceCreate true 每次都创建一个新的短链； false 先检查原始地址是否存在，不存在则创建，否则直接返回已存在的短链
     * @return 短链地址
     */
    public String toShortUrl(String url, boolean forceCreate) {
        String shortUrlRoot = requireShortUrlRoot();
        Link link = createLink(url, forceCreate);
        String shortCode = SysConvert.toX(link.getId());
        String shortUrl = String.format("%s/%s", shortUrlRoot, shortCode);
        log.debug("短链创建成功：originUrl={}, shortUrl={}, id={}", url, shortUrl, link.getId());
        return shortUrl;
    }

    public Link createLink(String url, boolean forceCreate) {
        Link link;
        if (forceCreate || ((link = linkRepository.findFirstByUrl(url).orElse(null)) == null)) {
            link = new Link();
            link.setUrl(url);
            parseUrlToLink(url, link);
            linkRepository.save(link);
        }
        return link;
    }

    public CreateLinkResponseDto generateShortUrl(CreateLinkDto dto) {
        String shortUrlRoot = requireShortUrlRoot();
        Link link = createLink(dto.getUrl(), false);
        String shortCode = SysConvert.toX(link.getId());
        return new CreateLinkResponseDto(shortUrlRoot + "/" + shortCode, shortCode);
    }

    public boolean parseUrlToLink(String url, Link link) {
        if (url != null && link != null) {
            String surveyClientPrefix = requireSurveyClientPrefix();
            String[] as = link.getUrl().split("\\?"); //https://test.xmplus.cn/lite/2282797748760576?channelId=3195664636250112&collectorMethod=SHORT_LINK
            if (as.length == 2) {
                String s1 = as[0]; // https://test.xmplus.cn/lite/2282797748760576
                String[] as1 = s1.split("/");
                if (as1.length >= 5 && "lite".equals(as1[3]) && NumberUtils.isDigits(as1[4])) {
                    Long surveyId = Long.parseLong(as1[4]);
                    String s2 = as[1]; // channelId=3195664636250112&collectorMethod=SHORT_LINK
                    Map<String, Object> params = new HashMap<>();
                    params.put("surveyId", surveyId);
                    Map<String, Object> nestParams = new HashMap<>();
                    String[] as2 = s2.split("&");
                    Arrays.stream(as2).forEach(i -> {
                        String[] is = i.split("=");
                        if (is.length == 2) {
                            if (is[0].startsWith("_")) {
                                nestParams.put(is[0], is[1]);
                            } else {
                                params.put(is[0], is[1]);
                            }
                        }
                    });
                    if (!nestParams.isEmpty()) {
                        params.put("parameters", nestParams);
                    }
                    link.setSurveyId(surveyId);
                    link.setSurveyClientPrefix(surveyClientPrefix);
                    link.setParams(JsonHelper.toJson(params));
                    return true;
                }
            }
        }
        return false;
    }

    private String requireSurveyClientPrefix() {
        return Optional.ofNullable(getClientConfig().getClientPrefix())
                .filter(StringUtils::isNotEmpty)
                .orElseThrow(() -> new BadRequestException("答题端地址前缀未配置"));
    }

    private String requireShortUrlRoot() {
        return Optional.ofNullable(getClientConfig().getClientRoot())
                .filter(StringUtils::isNotEmpty)
                .orElseThrow(() -> new BadRequestException("短链地址前缀未配置"));
    }

    public String encodeUrl(String url) {
        //正则表达式匹配参数中的中文
        Pattern pattern = Pattern.compile("([\u4e00-\u9fa5]+)");
        Matcher matcher = pattern.matcher(url);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {  //匹配有中文，进行编码并替换
            String resultStr = null;
            try {
                resultStr = URLEncoder.encode(matcher.group(1), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            matcher.appendReplacement(sb, resultStr);
        }

        matcher.appendTail(sb);
        return sb.toString();
    }
}
