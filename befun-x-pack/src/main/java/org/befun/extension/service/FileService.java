package org.befun.extension.service;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.common.file.storage.MockMultipartFile;
import cn.hanyi.common.file.storage.UploadPretreatment;
import cn.hanyi.common.file.storage.UploadPretreatment;
import cn.hanyi.common.file.storage.platform.FileStorage;
import cn.hanyi.common.file.storage.platform.LocalFileStorage;
import cn.hanyi.common.file.storage.recorder.FileRecorder;
import com.glaforge.i18n.io.CharsetToolkit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.befun.core.dto.UserDto;
import org.befun.core.exception.BadRequestException;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.core.service.ShareTokenCheckService;
import org.befun.core.utils.RegHelper;
import org.befun.core.utils.SafeStringUtils;
import org.befun.extension.annotations.CsvFieldOrderAnnotation;
import org.befun.extension.dto.CsvDto;
import org.befun.extension.dto.FileDto;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service("xPackFileService")
@ConditionalOnProperty(name = "hanyi.common.file-storage.default-platform")
public class FileService implements FileRecorder {

    final static String CSV_TYPE = "text/csv";
    final static String TYPE_ERROR = "文件类型错误";
    final static String DATA_ERROR = "数据内容格式错误";
    final static String PARSER_ERROR = "CSV解析错误";
    @Lazy
    @Autowired
    public FileStorageService fileStorageService;
    @Autowired
    private ShareTokenCheckService shareTokenCheckService;
    public String privatePlatform = "private";


    @Value("${hanyi.common.file-storage.enable-delete:false}")
    private boolean enableDelete;
    protected static final String TOKEN = "a146efda-f7f3-4a65-8607-dd7fb58263e5";


    @Autowired
    private LegacyAuthTokenFilter legacyAuthTokenFilter;
    @Value("${lite.download.auth.enable:true}")
    private boolean enableDownloadUserToken;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    private final static  String DOWNLOAD_URL_KEY = "download:url";



    @Override
    public boolean record(FileInfo fileInfo) {
        return true;
    }

    public FileInfo upload(MultipartFile file, Optional<Boolean> isPrivate) {
        UploadPretreatment pretreatment = fileStorageService.of(file);
        if (isPrivate.orElse(false)) {
            pretreatment.setPlatform(privatePlatform);
        }
        return pretreatment.upload();
    }
    @SneakyThrows
    public FileInfo uploadWithToken(MultipartFile file, String path, String token, Boolean useFileName) {
        if (!TOKEN.equals(token)) {
            throw new BadRequestException("error token");
        }

        if (useFileName != null && useFileName) {
            MockMultipartFile mockMultipartFile = new MockMultipartFile(file.getOriginalFilename(), file.getOriginalFilename(), MediaType.MULTIPART_FORM_DATA_VALUE, file.getBytes());
            return upload(mockMultipartFile, path, URLEncoder.encode(Objects.toString(file.getOriginalFilename()), StandardCharsets.UTF_8));
        }

        return upload(file, path, null);
    }

    public FileInfo upload(MultipartFile file, String path, String fileName, Boolean encode) {
        return upload(file, path, buildFileName(file, false));
    }

    public FileInfo upload(MultipartFile file, String path, String fileName) {
        FileStorage fileStorage = fileStorageService.getFileStorage();
        log.info("upload original file name: {}, file name:{}", file.getOriginalFilename(), file.getName());
        String oldBasePath = fileStorage.getBasePath();
        try {
            if (path != null) {
                String basePath = path + "/";
                if (LocalFileStorage.class == fileStorageService.getFileStorage().getClass()) {
                    basePath += "/";
                }
                fileStorage.setBasePath(basePath);
            }
            UploadPretreatment uploadPretreatment = fileStorageService.of(file);
            if (StringUtils.isEmpty(fileName)) {
                fileName = buildFileName(file, true);
            }
            fileName = SafeStringUtils.safeTitle(fileName);
            uploadPretreatment.setSaveFilename(fileName);
            uploadPretreatment.setName(fileName);
            FileInfo fileInfo = uploadPretreatment.upload();
            log.info("upload file url: {}", fileInfo.getUrl());
            return fileInfo;
        } finally {
            fileStorage.setBasePath(oldBasePath);
        }
    }
    private String buildFileName(MultipartFile file, Boolean encode) {
        encode = encode == null || encode;
        // 文件名_时间戳.后缀
        String fileOriginalName = file.getOriginalFilename();
        String suffix = null;
        if (StringUtils.isNotEmpty(fileOriginalName)) {
            int index = fileOriginalName.lastIndexOf(".");
            if (index >= 0) {
                suffix = fileOriginalName.substring(index);
                fileOriginalName = fileOriginalName.substring(0, index);
            }
        }
        if (StringUtils.isEmpty(fileOriginalName)) {
            fileOriginalName = UUID.randomUUID().toString();
        } else {
            fileOriginalName = encode ? URLEncoder.encode(fileOriginalName, StandardCharsets.UTF_8) : fileOriginalName;
        }
        if (StringUtils.isEmpty(suffix)) {
            try {
                String s = file.getContentType();
                if (StringUtils.isNotEmpty(s)) {
                    ContentType contentType = ContentType.parse(s);
                    String mimeType = contentType.getMimeType();
                    if (mimeType != null) {
                        String[] ss = mimeType.split("/");
                        if (ss.length == 2 || ss[0].equalsIgnoreCase("image")) {
                            suffix = "." + ss[1];
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("files upload content parse error {}", e.getMessage());
            }
        }
        if (suffix == null) {
            suffix = "";
        } else {
            suffix = encode ? URLEncoder.encode(suffix, StandardCharsets.UTF_8) : suffix;
        }
        return fileOriginalName + "_" + System.currentTimeMillis() + suffix;
    }

    @SneakyThrows
    public byte[] download(FileDto params, HttpServletResponse response) {
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(params.getUrl());
        if (StringUtils.isNotEmpty(params.getToken())) {
            UserDto userDto = legacyAuthTokenFilter.fetchUserInfoByToken(params.getToken());
            if (userDto != null) {
                if(enableDownloadUserToken){
                    String downloadUrlKey = DOWNLOAD_URL_KEY + ":" + fileInfo.getFilename();
                    String userId = stringRedisTemplate.opsForValue().get(downloadUrlKey);
                    if (userId != null && !userId.equals(userDto.getId().toString())) {
                        // 非生成的下载文件不允许下载
                        return new byte[0];
                    }
                }
                fileInfo.setPlatform(privatePlatform);
                fileInfo.setBasePath(fileStorageService.getFileStorage(privatePlatform).getBasePath());
            }
        }

        String contentDisposition = params.getDownload() ? "attachment" : "inline";
        if(!params.getDownload()){
            // 非下载的一定是图片
            response.setContentType(MediaType.IMAGE_JPEG_VALUE);
        }
        response.setHeader("content-disposition", String.format("%s;filename=%s", contentDisposition, URLEncoder.encode(fileInfo.getFilename(), "UTF-8")));
        return fileStorageService.download(fileInfo).bytes();
    }

    @SneakyThrows
    public byte[] download(String url, HttpServletResponse response) {
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(url);
        response.setHeader("content-disposition", String.format("attachment;filename=%s", URLEncoder.encode(fileInfo.getFilename(), "UTF-8")));
        String oldBasePath = fileStorageService.getFileStorage().getBasePath();
        try {
            Matcher matcher = Pattern.compile("/*.*/").matcher(url);
            if (matcher.find()) {
                String basePath = matcher.group(0);
                fileStorageService.getFileStorage().setBasePath(basePath);
                if (LocalFileStorage.class == fileStorageService.getFileStorage().getClass()) {
                    basePath += "/";
                }
                fileInfo.setBasePath(basePath);
            }

            if (fileInfo == null) {
                throw new BadRequestException(String.format("url:%s not found", url));
            }

            return fileStorageService.download(fileInfo).bytes();
        } finally {
            fileStorageService.getFileStorage().setBasePath(oldBasePath);
        }
    }

    @SneakyThrows
    public byte[] download(String url, Consumer<String> fileName) {
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(url);
        fileName.accept(URLEncoder.encode(fileInfo.getFilename(), "UTF-8"));
        String oldBasePath = fileStorageService.getFileStorage().getBasePath();
        try {
            Matcher matcher = Pattern.compile("/*.*/").matcher(url);
            if (matcher.find()) {
                String basePath = matcher.group(0);
                fileStorageService.getFileStorage().setBasePath(basePath);
                if (LocalFileStorage.class == fileStorageService.getFileStorage().getClass()) {
                    basePath += "/";
                }
                fileInfo.setBasePath(basePath);
            }

            if (fileInfo == null) {
                throw new BadRequestException(String.format("url:%s not found", url));
            }

            return fileStorageService.download(fileInfo).bytes();
        } finally {
            fileStorageService.getFileStorage().setBasePath(oldBasePath);
        }
    }

    @Override
    public FileInfo getByUrl(String s) {
        try {
            FileStorage storagePlatform = fileStorageService.getFileStorage();
            FileInfo fileInfo = new FileInfo();
            String filename = RegHelper.parseFileName(s);
            fileInfo.setBasePath(storagePlatform.getBasePath());
            fileInfo.setFilename(filename);
            fileInfo.setPath("");
            fileInfo.setUrl(fileInfo.getBasePath() + filename);
            fileInfo.setPlatform(storagePlatform.getPlatform());
            log.info("get file:{} info by url: {} ", fileInfo.getFilename(), s);
            return fileInfo;
        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;

    }

    @Override
    public boolean delete(String s) {
        try {
            if (enableDelete) {
                FileInfo file = fileStorageService.getFileInfoByUrl(s);
                log.info("delete file:{} by url: {} ", file.getFilename(), s);
                return fileStorageService.delete(file, fileInfo -> fileStorageService.exists(fileInfo));
            }
        } catch (Exception e) {
            log.error("file service delete:{} error: {}", s, e.toString());
        }
        return false;
    }

    /**
     * 解析csv文件
     * 需要定义好csv的DTO，使用@CsvFieldOrderAnnotation注解标明字段顺序和字段名称
     * DTO需要由无惨构造
     *
     * @param multipartFile
     * @param dtoClass
     * @param <T>
     * @return CSVDto or null
     */
    public <T> CsvDto parseCsvFile(MultipartFile multipartFile, Class<T> dtoClass) {
        if (!CSV_TYPE.equalsIgnoreCase(multipartFile.getContentType())) {
            log.error("{}: {}", TYPE_ERROR, multipartFile.getOriginalFilename());
            return null;
        }

        CsvDto<T> csvDto = new CsvDto<>();
        csvDto.setName(multipartFile.getOriginalFilename());
        csvDto.setSize(multipartFile.getSize());

        try {

            InputStream is = multipartFile.getInputStream();
            Charset charset = guessCharset(is);
            CSVParser csvRecords = CSVParser.parse(new BOMInputStream(multipartFile.getInputStream()), charset, CSVFormat.DEFAULT);
            List<CSVRecord> records = csvRecords.getRecords();

            // 无数据返回null
            if (records.size() < 2) {
                log.error("{}: {}", DATA_ERROR, multipartFile.getOriginalFilename());
                return null;
            }

            // 获取带CsvFieldOrderAnnotation的字段并且排序
            Field[] fields = Arrays.stream(dtoClass.getDeclaredFields())
                    .filter(field -> field.isAnnotationPresent(CsvFieldOrderAnnotation.class))
                    .sorted(Comparator.comparingInt(field -> field.getAnnotation(CsvFieldOrderAnnotation.class).order()))
                    .toArray(Field[]::new);

            List<String> headers = Arrays.stream(fields).map(field -> field.getAnnotation(CsvFieldOrderAnnotation.class).name()).collect(Collectors.toList());

            // dto的字段与csv文件的字段不匹配
            if (headers.size() != records.subList(0, 1).get(0).size()) {
                log.error("{}: {}", DATA_ERROR, multipartFile.getOriginalFilename());
                return null;
            }

            csvDto.setHeader(headers);
            List<CSVRecord> rows = records.subList(1, records.size());

            for (CSVRecord row : rows) {
                T rowObj = dtoClass.getConstructor().newInstance();
                for (int i = 0; i < fields.length; i++) {
                    PropertyUtils.setProperty(
                            rowObj,
                            fields[i].getName(),
                            ConvertUtils.convert(row.get(i), fields[i].getType()));
                }
                csvDto.getRows().add(rowObj);
            }

        } catch (Exception e) {
            log.error("{}: {} with {}", PARSER_ERROR, multipartFile.getOriginalFilename(), e.toString());
            return null;
        }
        return csvDto;
    }

    /**
     * 获取字符编码
     *
     * @param is
     * @return
     */
    private Charset guessCharset(InputStream is) {
        Charset charset = null;
        try {
            byte[] buffer = new byte[1024];
            if (is.read(buffer) != -1) {
                CharsetToolkit toolkit = new CharsetToolkit(buffer);
                toolkit.setDefaultCharset(Charset.forName("GBK"));
                is.reset();
                charset = toolkit.guessEncoding();
            }
        } catch (Exception e) {
            //ignore
        }
        return charset == null ? StandardCharsets.UTF_8 : charset;
    }

}
