package org.befun.extension.service;

import org.befun.core.exception.BadRequestException;
import org.befun.extension.constant.LockKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

@Service
public class LockRunnableHelper {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public void run(LockKey lockKey, Object args, Runnable runnable) {
        runOrThrow(lockKey, args, runnable, null);
    }

    public void runOrThrow(LockKey lockKey, Object args, Runnable runnable) {
        runOrThrow(lockKey, args, runnable, () -> new BadRequestException("处理中"));
    }

    public void runOrThrow(LockKey lockKey, Object args, Runnable runnable, Supplier<? extends RuntimeException> throwable) {
        runOrThrow(lockKey, args == null ? null : List.of(args), runnable, throwable);
    }

    public void run(<PERSON><PERSON><PERSON> lockKey, List<Object> args, Runnable runnable) {
        runOrThrow(lockKey, args, runnable, null);
    }

    public void runOrThrow(LockKey lockKey, List<Object> args, Runnable runnable) {
        runOrThrow(lockKey, args, runnable, () -> new BadRequestException("处理中"));
    }

    public void runOrThrow(LockKey lockKey, List<Object> args, Runnable runnable, Supplier<? extends RuntimeException> throwable) {
        String key = lockKey.key(args == null ? null : args.toArray());
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofSeconds(10));
        if (lock != null && lock) {
            try {
                runnable.run();
            } finally {
                stringRedisTemplate.delete(key);
            }
        } else {
            RuntimeException e = throwable == null ? null : throwable.get();
            if (e != null) {
                throw e;
            }
        }
    }
}