package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.befun.extension.Extensions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Order(XPackFilter.ORDER_HTTP_LOG)
@Component("xPackHttpLogFilter")
@ConditionalOnProperty(name = Extensions.HTTP_LOG_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.HTTP_LOG_MATCH_IF_MISSING)
public class HttpLogFilter implements WebInternalFilter {

    public List<String> ignorePath = List.of(
            "v3/api-docs",
            "webjars",
            "inbox-messages/count",
            "swagger-ui",
            "actuator",
            "embed"
    );

    @Override
    public void postFilter(XPackFilterContext context) {
        try {
            log(context);
        } catch (Throwable e) {
            log.error("日志记录：打印日志失败，忽略错误{}", e.getMessage());
        }
    }

    private void log(XPackFilterContext context) {
        String path = context.getRequest().getServletPath();
        if (path != null && ignorePath.stream().anyMatch(path::contains)) {
            return;
        }

        RequestInfoContext.Data logData = RequestInfoContext.get();
        if (logData == null) {
            return;
        }
        if (context.applyMatchResponseContentType(null)) {
            logData.setResponseBody(context.getWrapperResponse().getResponseString());
        }
        int httpStatus = context.getResponse().getStatus();
        if (context.getThrowable() != null && httpStatus == 200) {
            // 部分异常抛出之后，此时还没设置 httpStatus，导致这里获取的是默认值 200，直接修改为 500
            logData.setResponseStatus(500);
        } else {
            logData.setResponseStatus(httpStatus);
        }

        StringBuilder s = new StringBuilder();
        s.append("\n");
        s.append("****************************************************************************************************************\n");

        s.append("--- method   ---\n");
        s.append(logData.getMethod());
        s.append("\n");

        s.append("--- path     ---\n");
        s.append(logData.getUrl());
        s.append("\n");

        Optional.ofNullable(logData.getToken()).ifPresent(i -> {
            s.append("--- token    ---\n");
            s.append(logData.getToken());
            s.append("\n");
        });

        Optional.ofNullable(logData.getUser()).ifPresent(i -> {
            s.append("--- user     ---\n");
            s.append(logData.getUser());
            s.append("\n");
        });

        Optional.ofNullable(logData.getRequestParams()).ifPresent(i -> {
            s.append("--- param    ---\n");
            s.append(logData.getRequestParams());
            s.append("\n");
        });

        Optional.ofNullable(logData.getRequestBody()).ifPresent(i -> {
            s.append("--- requestBody ---\n");
            s.append(logData.getRequestBody());
            s.append("\n");
        });

        s.append("--- cost     ---\n");
        s.append(System.currentTimeMillis() - logData.getStart());
        s.append(" ms");
        s.append("\n");

        s.append("--- response ---\n");
        s.append(logData.getResponseStatus());
        s.append("\n");
        s.append(logData.getResponseBody());
        s.append("\n");

        s.append("****************************************************************************************************************\n");
        log.info(s.toString());
    }

}
