package org.befun.extension.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.befun.extension.Extensions;
import org.befun.extension.property.SqlProperty;
import org.befun.extension.property.XPackFilterProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

@Slf4j
@Order(XPackFilter.ORDER_SQL)
@Component("xPackSQLFilter")
@ConditionalOnProperty(name = Extensions.SQL_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.SQL_MATCH_IF_MISSING)
public class SQLFilter implements WebInternalFilter {

    @Autowired
    private SqlProperty sqlProperty;
    @Autowired
    private XPackFilterProperty xPackFilterProperty;

    @Override
    public Boolean preFilter(XPackFilterContext context) throws IOException {
        if (isSkip(context)) {
            return true;
        }
        if (validate(context.getWrapperRequest())) {
            return true;
        } else {
            context.getResponse().sendError(HttpServletResponse.SC_BAD_REQUEST, "输入的内容含非法数据，请按现有的参数使用");
            return false;
        }
    }

    private Boolean isSkip(XPackFilterContext context) {
        if(context.getFullUrl().contains("/files")){
            return true;
        }
        return sqlProperty == null || sqlProperty.getBlackList().isEmpty();
    }

    private Boolean validate(HttpServletRequestWrapper requestWrapper) throws IOException {

        String params = requestWrapper.getQueryString();
        String data = StringEscapeUtils.unescapeJava(StringEscapeUtils.unescapeEcmaScript(StringEscapeUtils.unescapeHtml4(requestWrapper.getData())));

        AtomicReference<Boolean> error = new AtomicReference<>(false);

        Optional.ofNullable(params).ifPresent(p -> {
            if (validateBlackList(p)) {
                error.set(true);
            }
        });

        Optional.ofNullable(data).ifPresent(d -> {
            if (validateBlackList(d)) {
                error.set(true);
            }
        });

        return !error.get();
    }

    private Boolean validateBlackList(String data) {
            for (String pattern : sqlProperty.getBlackList()) {
                if (Pattern.compile(pattern.toLowerCase()).matcher(data.toLowerCase()).find()) {
                    return true;
                }
            }
            return false;
    }

}