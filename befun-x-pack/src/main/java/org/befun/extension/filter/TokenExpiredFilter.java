package org.befun.extension.filter;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.befun.core.security.LegacyAuthTokenFilter;
import org.befun.extension.Extensions;
import org.befun.extension.property.TokenExpiredProperty;
import org.befun.extension.property.XPackFilterProperty;
import org.befun.extension.property.XssProperty;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Order(XPackFilter.ORDER_TOKEN_EXPIRED)
@Component("xPackTokenExpiredFilter")
@ConditionalOnProperty(name = Extensions.TOKEN_EXPIRED_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.TOKEN_EXPIRED_MATCH_IF_MISSING)
public class TokenExpiredFilter implements WebInternalFilter {

    @Autowired
    private TokenExpiredProperty tokenExpiredProperty;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Boolean preFilter(XPackFilterContext context) {
        HttpServletRequestWrapper request = context.getWrapperRequest();
        Optional.ofNullable(request.getHeader(LegacyAuthTokenFilter.AUTHORIZATION)).ifPresent(token->{
            String path = request.getServletPath();
            if (path != null && tokenExpiredProperty.getSkipPrefixList().stream().noneMatch(path::contains)) {
                String key = LegacyAuthTokenFilter.getSessionKey(token);
                if (stringRedisTemplate.hasKey(key)) {
                    log.info("{}: {}: {}", token, path, tokenExpiredProperty.getExpiredMinutes());
                    stringRedisTemplate.expire(key, Duration.ofMinutes(tokenExpiredProperty.getExpiredMinutes()));
                }
            }
        });

        return true;
    }

}