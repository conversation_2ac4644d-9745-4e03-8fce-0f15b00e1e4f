package org.befun.extension.property;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "befun.extension.filter")
public class XPackFilterProperty {

    private List<PathReplaceRule> pathReplaceRules = new ArrayList<>();
    private List<String> whiteImgSuffix = List.of("jpeg", "jpg","png","gif","bmp");

    public List<PathReplaceRule> getPathReplaceRules() {
        if (pathReplaceRules.isEmpty()) {
            synchronized (this) {
                if (pathReplaceRules.isEmpty()) {
                    pathReplaceRules = new ArrayList<>();
                    pathReplaceRules.add(new PathReplaceRule("/[1-9]\\d*", "/*"));
                    pathReplaceRules.add(new PathReplaceRule("/cas-\\w*/cem", "/cas-*/cem"));
                }
            }
        }
        return pathReplaceRules;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PathReplaceRule {
        private String reg;
        private String replacement;
    }

}
