package org.befun.extension.property;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.extension.Extensions;
import org.befun.extension.constant.SafeListType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.befun.extension.constant.SafeListType.basicWithImages;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = Extensions.TOKEN_EXPIRED_PREFIX)
public class TokenExpiredProperty {

    private String skipPrefixList;
    private int expiredMinutes = 30;

    public ArrayList<String> getSkipPrefixList() {
        if (StringUtils.isEmpty(skipPrefixList)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(List.of(skipPrefixList.split(",")));
    }



}
