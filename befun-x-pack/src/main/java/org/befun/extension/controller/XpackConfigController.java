package org.befun.extension.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.extension.Extensions;
import org.befun.extension.entity.OperateLog;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.repository.OperateLogRepository;
import org.befun.extension.repository.XpackConfigRepository;
import org.befun.extension.service.AbstractOperateLogService;
import org.befun.extension.service.XpackConfigService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "x-pack管理")
@RestController
@ResourceController(
        entityClass = XpackConfig.class,
        serviceClass = XpackConfigService.class,
        repositoryClass = XpackConfigRepository.class,
        permission = "isAuthenticated()",
        docTag = "x-pack管理",
        docCrud = "x-pack管理"
)
@RequestMapping("/x-pack")
@PreAuthorize("isAuthenticated()")
@ConditionalOnProperty(name = Extensions.X_PACK_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.X_PACK_MATCH_IF_MISSING)
public class XpackConfigController {

}
