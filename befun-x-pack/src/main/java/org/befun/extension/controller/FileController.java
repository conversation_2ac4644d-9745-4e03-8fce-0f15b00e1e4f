package org.befun.extension.controller;

import cn.hanyi.common.file.storage.FileStorageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.SneakyThrows;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.utils.DateHelper;
import org.befun.extension.Extensions;
import org.befun.extension.dto.FileDto;
import org.befun.extension.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Optional;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;


@Slf4j
@Tag(name = "文件上传")
@RestController
@ConditionalOnProperty(name = Extensions.UPLOAD_FILE_ENABLE_KEY, havingValue = "true", matchIfMissing = Extensions.UPLOAD_FILE_MATCH_IF_MISSING)
@ConditionalOnBean({FileService.class, FileStorageService.class})
public class FileController {

    @Autowired
    private FileStorageService storageService;

    @Autowired
    private FileService fileService;

    private final Map<String, byte[]> fixedMap = Collections.synchronizedMap(new LinkedHashMap<String, byte[]>(10, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, byte[]> eldest) {
            return size() > 10;
        }
    });

    @SneakyThrows
    @RequestMapping(value = "/files", method = RequestMethod.POST)
    public ResourceResponseDto<String> upload(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "isPrivate", required = false) Optional<Boolean> isPrivate
    ) {
        return new ResourceResponseDto(fileService.upload(file, isPrivate).getUrl());
    }

    @RequestMapping(value = "/files-token", method = RequestMethod.POST)
    public ResourceResponseDto<String> uploadWithToken(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "token", required = false) String token,
            @RequestParam(value = "useFileName", required = false) Boolean useFileName,
            @RequestParam(value = "path", required = false
            ) String path
    ) {
        return new ResourceResponseDto<>(fileService.uploadWithToken(file, path, token, useFileName).getUrl());
    }

    @RequestMapping(value = "/files", method = RequestMethod.GET)
    public void download(HttpServletRequest request, HttpServletResponse response, @Valid FileDto params) throws IOException {
        byte[] bytes = fileService.download(params, response);

        int start = 0;
        int totalSize = bytes.length;
        int readSize = bytes.length;

        if (request.getHeader("Range") != null) {

            response.setStatus(206);
            String[] range = request.getHeader("Range").replace("bytes=", "").split("-");
            start = Integer.valueOf(range[0]);
            int end = range.length > 1 ? Integer.valueOf(range[1]) : readSize - 1;
            readSize = (end - start) + 1;
            response.setContentLength(readSize);
            response.setHeader("Content-Type", "video/mp4");
            response.setHeader("content-range", String.format("bytes %s-%s/%s", start, end, totalSize));
        }
        ServletOutputStream outputStream = response.getOutputStream();
        outputStream.write(bytes, start, readSize);
        outputStream.flush();
        outputStream.close();
    }

}
