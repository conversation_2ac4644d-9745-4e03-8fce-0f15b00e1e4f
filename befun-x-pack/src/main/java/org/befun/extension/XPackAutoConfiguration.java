package org.befun.extension;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.IpResolverService;
import lombok.extern.slf4j.Slf4j;
import org.befun.extension.property.*;
import org.befun.extension.service.MailService;
import org.befun.extension.service.NativeSqlHelper;
import org.befun.extension.service.SmsService;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
@ComponentScan({
        "org.befun.extension.toast",
        "org.befun.extension.sms",
        "org.befun.extension.service",
        "org.befun.extension.filter",
        "org.befun.extension.controller",
        "org.befun.extension.systemupdate",
        "org.befun.extension.captcha",
        "org.befun.extension.rserve",
})
@EnableConfigurationProperties({
        GraphCaptchaProperty.class,
        RserveProperty.class,
        WeChatMpProperty.class,
        AlipayProperty.class,
        ToastProperty.class,
        SmsProperty.class,
        XssProperty.class,
        SqlProperty.class,
        MailProperty.class,
        InboxMesasgeProperty.class,
        LinkProperty.class,
        WeChatOpenProperty.class,
        WeChatPayProperty.class,
        WeChatMiniProgramProperty.class,
        SmartVerifyProperty.class,
        TemplateFileProperty.class,
        OpenTokenProperty.class,
        XPackFilterProperty.class,
        TokenExpiredProperty.class,
})
//@EntityScan("org.befun.extension.entity")
//@EnableJpaRepositories(basePackages = "org.befun.extension.repository", repositoryBaseClass = BaseRepositoryImpl.class)
public class XPackAutoConfiguration {

    public static final String PACKAGE_ENTITY = "org.befun.extension.entity";
    public static final String PACKAGE_REPOSITORY = "org.befun.extension.repository";

    @Bean
    @ConditionalOnMissingBean(SmsService.class)
    public SmsService xPackSmsService() {
        return new SmsService.EmptySmsService();
    }

    @Bean
    @ConditionalOnMissingBean(MailService.class)
    public MailService xPackMailService() {
        return new MailService.EmptyMailService();
    }

    @Bean
    public NativeSqlHelper nativeSqlHelper() {
        return new NativeSqlHelper();
    }

    @Bean
    @ConditionalOnMissingBean(IpResolverProperties.class)
    public IpResolverProperties ipResolverProperties() {
        var properties = new IpResolverProperties();
        properties.setLocal(new IpResolverProperties.LocalConfig());
        return properties;
    }

    @Bean
    @ConditionalOnMissingBean(IpResolverService.class)
    public IpResolverService ipResolverService() {
        return new IpResolverService();
    }

    @Bean
    @ConditionalOnMissingBean
    public ISmsAccountService smsAccountService() {
        return new ISmsAccountService() {
        };
    }

}
