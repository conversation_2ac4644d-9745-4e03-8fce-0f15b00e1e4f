package org.befun.extension.sms.chuanglan;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.dto.SmsReceiveInfo;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.sms.BaseSmsProvider;
import org.befun.extension.sms.ISmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Component("chuanglan")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-chuanglan"}, havingValue = "true")
public class ChuanglanSmsProvider extends BaseSmsProvider<SmsProviderProperty.SmsChuanglan> {

    private static final String URL_TEXT = "https://smssh1.253.com/msg/v1/send/json";
    private static final String URL_TEMPLATE = "https://smssh1.253.com/msg/variable/json";
    private static final String URL_PULL = "https://smssh1.253.com/msg/pull/report";
    private SmsProviderProperty.SmsChuanglan chuanglan;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void init(SmsProviderProperty config) {
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        String globalSignature = chuanglan.getSignature();
        String globalRealSignature = chuanglan.getRealSignature();
        config.getTemplates().forEach(t -> {
            if (StringUtils.isEmpty(t.getSignature())) {
                t.setSignature(globalSignature);
            }
            if (StringUtils.isEmpty(t.getRealSignature())) {
                t.setRealSignature(globalRealSignature);
            }
        });
        super.init(config);
    }

    @Override
    public SmsProviderProperty.SmsChuanglan requireConfig(SmsProviderProperty config) {
        if (chuanglan != null) {
            return chuanglan;
        }
        Assert.notNull(config, "创蓝短信服务配置不能为空");
        Assert.notNull(config.getChuanglan(), "创蓝短信服务配置不能为空");
        chuanglan = config.getChuanglan();
        return chuanglan;
    }

    public SmsProviderProperty.SmsChuanglan requireConfig() {
        return requireConfig(null);
    }

    @Override
    public XPackAppType getTemplateConfigType() {
        return XPackAppType.SMS_TEMPLATE_CHUANGLAN;
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        // 如果没有变量，则尝试用纯文本的方式发送
        if (CollectionUtils.isEmpty(info.getVariableValues())) {
            SmsNotifyTextInfo textInfo = new SmsNotifyTextInfo(info.getMobile(), info.getContent(), info.getParams(), info.getRealSignature(), info.getOriginTemplate());
            boolean success = sendMessageByText(config, textInfo);
            info.setResponse(textInfo.getResponse());
            info.setThirdpartyMessageId(textInfo.getThirdpartyMessageId());
            info.setRealCost(textInfo.getRealCost());
            return success;
        }
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        log.info("send to chuanglan {} template:{}", info.getMobile(), info.getTemplateId());
        try {
            String realSignature = StringUtils.isEmpty(info.getRealSignature()) ? chuanglan.getRealSignature() : info.getRealSignature();
            // 【253云通讯】创蓝云通讯验证码是{$var}，祝您体验愉快
            String msg = realSignature + info.getOriginTemplate();
            // ***********,1234；***********,4321
            List<String> params = new ArrayList<>();
            params.add(info.getMobile());
            info.getVariableValues().forEach(i -> params.add(i.getValue()));

            Map<String, Object> data = new HashMap<>();
            data.put("account", chuanglan.getAppId());
            data.put("password", chuanglan.getAppSecret());
            data.put("msg", msg);
            data.put("params", String.join(",", params));
            data.put("report", "true");
            String body = JsonHelper.toJson(data);
            log.info("chuanglan message body: {}", body);
            String response = post(URL_TEMPLATE, body);
            info.setResponse(response);
            log.info("chuanglan message response: {}", response);
            ChuanglanResponse chuanglanResponse = JsonHelper.toObject(response, ChuanglanResponse.class);
            if (chuanglanResponse.success()) {
                info.setThirdpartyMessageId(chuanglanResponse.getMsgId());
                log.info("has sent to chuanglan {}", chuanglanResponse.getMsgId());
                return true;
            } else {
                log.error("failed to chuanglan code:{} message:{}", chuanglanResponse.getCode(), chuanglanResponse.getErrorMsg());
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }

    private static String post(String url, String body) throws IOException {
        return Request.Post(url)
                .connectTimeout(1000 * 30)
                .socketTimeout(1000 * 30)
                .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                .execute().returnContent().asString();
    }

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        SmsProviderProperty.SmsChuanglan chuanglan = requireConfig(config);
        log.info("send to chuanglan {} text:{}", info.getMobile(), info.getMobile());
        try {
            String realSignature = StringUtils.isEmpty(info.getRealSignature()) ? chuanglan.getRealSignature() : info.getRealSignature();
            // 【253云通讯】创蓝253云通讯欢迎您的测试，祝您体验愉快
            String msg = realSignature + info.getOriginTemplate();

            Map<String, Object> data = new HashMap<>();
            data.put("account", chuanglan.getAppId());
            data.put("password", chuanglan.getAppSecret());
            data.put("msg", msg);
            data.put("phone", info.getMobile());
            data.put("report", "true");
            String body = JsonHelper.toJson(data);
            log.info("chuanglan message body: {}", body);
            String response = post(URL_TEXT, body);
            info.setResponse(response);
            log.info("chuanglan message response: {}", response);
            ChuanglanResponse chuanglanResponse = JsonHelper.toObject(response, ChuanglanResponse.class);
            if (chuanglanResponse.success()) {
                info.setThirdpartyMessageId(chuanglanResponse.getMsgId());
                log.info("has sent to chuanglan {}", chuanglanResponse.getMsgId());
                return true;
            } else {
                log.error("failed to chuanglan code:{} message:{}", chuanglanResponse.getCode(), chuanglanResponse.getErrorMsg());
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }

    /**
     * 收到了创蓝短信结果的回调
     */
    public void receiveSmsCallback(ChuanglanReceiveInfo receiveInfo, Consumer<SmsReceiveInfo> consumer) {
        try {
            if (receiveInfo == null) {
                return;
            }
            String key = ISmsProvider.key1("chuanglan", receiveInfo.getMsgId());
            String value = stringRedisTemplate.opsForValue().get(key);
            if (value != null) {
                String[] as = value.split(":");
                if (as.length == 2 && NumberUtils.isDigits(as[1])) {
                    String fromType = as[0];
                    long fromId = Long.parseLong(as[1]);
                    SmsReceiveInfo smsReceiveInfo = new SmsReceiveInfo(fromType, fromId, receiveInfo.isSuccess(), receiveInfo.message());
                    consumer.accept(smsReceiveInfo);
                    // 这条消息已经处理了，直接清除掉缓存
                    stringRedisTemplate.delete(key);
                    stringRedisTemplate.delete(ISmsProvider.key2(fromType, fromId));
                }
            }
        } catch (Throwable e) {
            log.error("处理(创蓝)短信结果失败: {}", JsonHelper.toJson(receiveInfo), e);
        }
    }

    /**
     * 主动拉取创蓝短信结果
     */
    public void pullSmsCallback(Consumer<SmsReceiveInfo> consumer) {
        try {
            SmsProviderProperty.SmsChuanglan chuanglan = requireConfig();
            String body = String.format("{\"account\":\"%s\",\"password\":\"%s\",\"count\":\"%d\"}",
                    chuanglan.getAppId(), chuanglan.getAppSecret(), 100);
            boolean loop = true;
            while (loop) {
                String response = post(URL_PULL, body);
                log.info("拉取(创蓝)短信结果：body= {}, response= {}", body, response);
                ChuanglanPullInfo pullInfo = JsonHelper.toObject(response, ChuanglanPullInfo.class);
                if (pullInfo == null
                        || pullInfo.getRet() == null || pullInfo.getRet() != 0
                        || pullInfo.getResult() == null || pullInfo.getResult().isEmpty()) {
                    loop = false;
                } else {
                    pullInfo.getResult().forEach(receiveInfo -> receiveSmsCallback(receiveInfo, consumer));
                }
            }
        } catch (Throwable e) {
            log.error("拉取(创蓝)短信结果失败", e);
        }
    }


}
