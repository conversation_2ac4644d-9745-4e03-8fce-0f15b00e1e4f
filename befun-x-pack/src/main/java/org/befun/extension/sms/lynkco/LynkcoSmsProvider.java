package org.befun.extension.sms.lynkco;

import com.geely.gbop.api.GbopApiApp;
import com.geely.gbop.api.GbopApiClient;
import com.geely.gbop.api.GbopApiRequest;
import com.geely.gbop.api.contant.HttpContentType;
import com.geely.gbop.api.contant.HttpMethod;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.dto.SmsNotifyTextInfo;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.property.SmsProviderProperty;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.service.XpackConfigService;
import org.befun.extension.sms.ISmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("lynkco")
@ConditionalOnProperty(prefix = "befun.extension.sms", name = {"enable", "enable-lynkco"}, havingValue = "true")
public class LynkcoSmsProvider implements ISmsProvider<SmsProviderProperty.SmsLynkco> {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private XpackConfigService xpackConfigService;

    @Getter
    private Map<String, SmsTemplateProperty> templatePropertyMap = new HashMap<>();
    @Override
    public Map<String, SmsTemplateProperty> getTemplatePropertyMap() {
        return templatePropertyMap;
    }

    @Override
    public void init(SmsProviderProperty config) {
//        SmsProviderProperty.SmsLynkco lynkco = requireConfig(config);
//        String globalSignature = lynkco.getSignature();
//        String globalRealSignature = lynkco.getRealSignature();
//        config.getTemplates().forEach(t -> {
//            if (StringUtils.isEmpty(t.getSignature())) {
//                t.setSignature(globalSignature);
//            }
//            if (StringUtils.isEmpty(t.getRealSignature())) {
//                t.setRealSignature(globalRealSignature);
//            }
//        });
//
//        config.getTemplates().forEach(i -> templatePropertyMap.put(i.getName(), i));
//        gbopClient = new GbopApiClient(new GbopApiApp(lynkco.getAppKey(), lynkco.getAppSecret()), lynkco.getBaseUrl());
    }

    public SmsProviderProperty.SmsLynkco requireConfig() {
        return requireConfig(null);
    }

    @Override
    public XPackAppType getTemplateConfigType() {
        return XPackAppType.SMS_TEMPLATE_LYNKCO;
    }

    @Override
    public SmsProviderProperty.SmsLynkco requireConfig(SmsProviderProperty config) {
        XpackConfig xpackConfig = xpackConfigService.getConfigByTypeAndSubType(XPackAppType.SMS_PROVIDER, TenantContext.getCurrentTenant().toString());
        Assert.notNull(xpackConfig, "短信服务配置不能为空");
        Assert.notNull(xpackConfig.getConfig(), "短信服务配置不能为空");
        return JsonHelper.toObject(xpackConfig.getConfig(), SmsProviderProperty.SmsLynkco.class);
    }

    @Override
    public boolean sendMessageByTemplate(SmsProviderProperty config, SmsNotifyInfo info) {
        log.info("send to lynkco {} template:{}", info.getMobile(), info.getTemplateId());
        try {
            SmsProviderProperty.SmsLynkco lynkco = requireConfig();

            Map<String, Object> data = new HashMap<>();
            data.put("sceneCode", info.getTemplateId());
            data.put("receiverId", info.getMobile());
            data.put("sceneParams", info.getParams());

            String body = JsonHelper.toJson(data);

            log.info("lynkco request: {}{}----{}", lynkco.getBaseUrl(), lynkco.getPath(), body);

            GbopApiRequest apiRequest = GbopApiRequest.buider().method(HttpMethod.POST)
                    .addHead("accessId", lynkco.getAccessId())
                    .contentType(HttpContentType.JSON)
                    .path(lynkco.getPath())
                    .body(RequestBody.create(MediaType.parse(HttpContentType.JSON.getValue()), body));
            GbopApiClient gbopClient = new GbopApiClient(new GbopApiApp(lynkco.getAppKey(), lynkco.getAppSecret()), lynkco.getBaseUrl());
            String response = gbopClient.syncHttpCall(apiRequest).getBody();

            info.setResponse(response);
            log.info(response);
            LynkcoResponse lynkcoResponse = JsonHelper.toObject(response, LynkcoResponse.class);
            if (lynkcoResponse.success()) {
                info.setThirdpartyMessageId(lynkcoResponse.getData());
                log.info("has lynkco {}", lynkcoResponse.getData());
                return true;
            } else {
                log.error("failed to lynkco: {}", response);
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean sendMessageByText(SmsProviderProperty config, SmsNotifyTextInfo info) {
        return false;
    }
}
