package org.befun.extension.sms.chuanglan;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.math.NumberUtils;


@Setter
@Getter
public class ChuanglanResponse {

    private String failNum;

    private String time;

    private String successNum;

    private String msgId;

    private String errorMsg;

    private String code;

    public boolean success() {
        return "0".equals(code) && NumberUtils.isDigits(successNum) && Integer.parseInt(successNum) > 0;
    }
}